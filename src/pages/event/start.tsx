/** 活动页面 */
import { Button } from '@/components/ui/shad/button'
import { useAtom } from 'jotai'
import { selectedEventDetailAtom, screenOrientationAtom } from '@/stores'
import { useAtomValue } from 'jotai'
import { usePreloadResource } from '@/hooks/usePreloadResource'
import { useNavigate } from 'react-router-dom'
import { useIsMobile } from '@/hooks/useIsMobile'
import { useEffect, useState } from 'react'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'
import SourceLoadStatus from '@/components/business/SourceLoadStatus'
import { scaleAtom } from '@/stores'
import { getScaleStyle } from '@/utils'
import { publicPreLoadSourceObj } from '@/configs/source'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { EventItem } from '@/apis/types'
import { SvgIcon } from '@/components/ui/SvgIcon'
import Img_logo from '/images/common/logo.png'

function StartPage() {
  const [errorText, setErrorText] = useState('')
  const [curEventDetail, setCurEventDetail] = useState<EventItem>()
  const { fetchResources } = usePreloadResource()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const scale = useAtomValue(scaleAtom)
  const [selectEventDetail] = useAtom(selectedEventDetailAtom)
  const { t } = useTranslation()
  const navigate = useNavigate()
  const isMobile = useIsMobile()
  const isLandSpace = isMobile ? false : screenOrientation.isLandScape

  useEffect(() => {
    fetchResources(true)
    if (window.innerWidth <= 768) {
      getCurEventData()
    }
  }, [])
  const getCurEventData = async () => {
    const res = await _ajax.get(_api.event_list)
    const data = res?.data?.data?.data
    const curData = data.find((it: any) => it.id === selectEventDetail?.id)
    setCurEventDetail(curData)
  }
  useEffect(() => {
    const _curEventDetail = curEventDetail as EventItem
    if (
      +new Date() < _curEventDetail?.start_time * 1000 ||
      +new Date() > _curEventDetail?.stop_time * 1000
    ) {
      setErrorText(
        t('该设备所选择活动已过期，请修改活动结束时间或选择另一个活动后再试。')
      )
    }
  }, [curEventDetail])

  const handleStart = async () => {
    navigate('/home')
  }

  if (errorText) {
    return (
      <SourceLoadStatus
        loadStatus={'fail'}
        errorText={errorText}
        style={{
          ...getScaleStyle({
            transform: `scale(${scale})`,
            padding: '0 10rem',
            textAlign: 'center',
          }),
        }}
        isReloadBtn={true}
      />
    )
  }
  return (
    <>
      <div
        className="px-24 py-[20px] w-full h-full flex flex-col justify-center items-center"
        style={{
          background: `url(${isLandSpace ? publicPreLoadSourceObj.silentCrosswiseBgImg : publicPreLoadSourceObj.silentVerticalBgImg}) no-repeat center/cover`,
        }}
      >
        <img
          className="h-[6.375rem]"
          src={Img_logo}
          style={{
            marginBottom: isLandSpace ? '3.125rem' : '4.375rem',
          }}
        ></img>
        <p
          className="maze-primary-text text-[3.5rem] font-[600] leading-[4.875rem] text-center line-clamp-2"
          style={{
            marginBottom: isLandSpace ? '2.125rem' : '3.375rem',
          }}
        >
          {selectEventDetail?.desc}
        </p>
        <h1 className="maze-primary-text text-[3.5rem] mb-[5.125rem] leading-[4.875rem] font-[600]">
          Snap a selfie!
        </h1>
        <div
          className="border-4 px-[0.5625rem] py-[0.5625rem] rounded-[150px] animate-button-float"
          style={{
            borderColor: 'rgba(86, 73, 204, 0.50)',
            background:
              'radial-gradient(50% 50% at 50% 50%, rgba(11, 10, 87, 0.60) 0%, rgba(2, 6, 52, 0.60) 100%)',
            backdropFilter: 'blur(5px)',
          }}
        >
          <Button
            className={classNames(
              'flex maze-bg-gradient-btn w-[18rem] h-[18rem] text-[2.82rem] rounded-full italic animate-breathing-glow hover:scale-105'
            )}
            variant="outline"
            onClick={handleStart}
          >
            Let‘s go!
          </Button>
        </div>
        <div
          className="flex gap-[4px] py-[1.625rem] px-[1.625rem] max-w-[46.8125rem] rounded-[2rem] maze-primary-text border-2 border-white/40"
          style={{
            background: 'rgba(108, 108, 108, 0.40)',
            backdropFilter: 'blur(9px)',
            marginTop: isLandSpace ? '4rem' : '6.25rem',
          }}
        >
          <SvgIcon
            src="/images/icons/tips.svg"
            alt="提示"
            className="w-[2.2rem] h-[2.2rem] mt-[0.4rem] flex-shrink-0"
          />
          <p className="font-[400] text-[2rem] leading-[3rem]">
            Your photo will be uploaded and may become visible to others. Only
            continue if you agree to the Terms of Service and Privacy Policy.
          </p>
        </div>

        <div
          className={
            isLandSpace ? 'mt-[1.375rem]' : 'absolute bottom-[3.125rem]'
          }
        >
          <a
            href="https://maze.guru/mirror/service_en"
            className="maze-primary-text text-[1.5rem] font-[400]"
            style={{
              background: 'linear-gradient(180deg, #F7A5FF 0%, #8676FF 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Term of Service
          </a>
          <span className="text-white mx-[1.25rem]">|</span>
          <a
            href="https://maze.guru/mirror/privacy_en"
            className="maze-primary-text text-[1.5rem] font-[400]"
            style={{
              background: 'linear-gradient(180deg, #F7A5FF 0%, #8676FF 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Privacy Policy
          </a>
        </div>
      </div>
    </>
  )
}

export default StartPage
